import React, { useState, useEffect } from 'react';
import clsx from 'clsx';
import Link from '@docusaurus/Link';
import Layout from '@theme/Layout';
import Translate, { translate } from '@docusaurus/Translate';
import Heading from '@theme/Heading';
import styles from './index.module.css';
import Footer from '../components/Footer';
import FAQSection from '../components/FAQSection';
import ImageModal from '../components/ImageModal';
import GoogleAccountAnalytics from '../components/GoogleAccountAnalytics';
import TestimonialsSection from '../components/TestimonialsSection';
import CTASection from '../components/CTASection';
import ExtensionStructuredData from '../components/ExtensionStructuredData';
import BenefitsSection from '../components/BenefitsSection';
import UseCasesSection from '../components/UseCasesSection';
import ComparisonSection from '../components/ComparisonSection';
import VideoSection from '../components/VideoSection';

function DifferentiationSection() {
    return (
        <section id="differentiation" className={styles.featureSection} style={{ backgroundColor: '#f3f3f8' }}>
            <div className="container">
                <Heading as="h2" className={styles.sectionTitle}>
                    <Translate id="extension_welcome.differentiation.title">
                        Beyond Basic AI Assistance: Cultivating Higher-Order Thinking
                    </Translate>
                </Heading>
                <p className={styles.sectionDescription}>
                    <Translate id="extension_welcome.differentiation.subtitle">
                        While other browser AI assistants focus on task completion, FunBlocks AI Assistant prioritizes developing your critical thinking and cognitive abilities
                    </Translate>
                </p>

                <div className="row" style={{ marginTop: '3rem', gap: 20 }}>
                    {/* <div className="col-lg-6"> */}
                    <div style={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        borderRadius: '12px',
                        padding: '2rem',
                        color: 'white',
                        flex: 1
                    }}>
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1.5rem' }}>
                            <div style={{
                                background: 'rgba(255, 255, 255, 0.2)',
                                borderRadius: '50%',
                                width: '60px',
                                height: '60px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginRight: '1rem'
                            }}>
                                <span style={{ fontSize: '24px' }}>💡</span>
                            </div>
                            <Heading as="h3" style={{ margin: 0, color: 'white' }}>
                                <Translate id="extension_welcome.differentiation.funblocks_title">
                                    FunBlocks AI Assistant
                                </Translate>
                            </Heading>
                        </div>

                        <div style={{ marginBottom: '1.5rem' }}>
                            <h4 style={{ color: 'rgba(255, 255, 255, 0.9)', marginBottom: '1rem' }}>
                                <Translate id="extension_welcome.differentiation.funblocks_focus">
                                    Focus: Enhancing Human Intelligence
                                </Translate>
                            </h4>
                            <ul style={{ listStyle: 'none', padding: 0 }}>
                                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '0.8rem' }}>
                                    <span style={{ marginRight: '0.5rem', color: '#4ade80' }}>✓</span>
                                    <Translate id="extension_welcome.differentiation.funblocks_feature1">
                                        Visual mind mapping for comprehensive information presentation
                                    </Translate>
                                </li>
                                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '0.8rem' }}>
                                    <span style={{ marginRight: '0.5rem', color: '#4ade80' }}>✓</span>
                                    <Translate id="extension_welcome.differentiation.funblocks_feature2">
                                        Multi-perspective exploration to avoid information silos
                                    </Translate>
                                </li>
                                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '0.8rem' }}>
                                    <span style={{ marginRight: '0.5rem', color: '#4ade80' }}>✓</span>
                                    <Translate id="extension_welcome.differentiation.funblocks_feature3">
                                        Critical analysis tools: bias detection, counterexamples, comprehensive analysis
                                    </Translate>
                                </li>
                                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '0.8rem' }}>
                                    <span style={{ marginRight: '0.5rem', color: '#4ade80' }}>✓</span>
                                    <Translate id="extension_welcome.differentiation.funblocks_feature4">
                                        Structured thinking frameworks (Six Thinking Hats, SWOT, First Principles)
                                    </Translate>
                                </li>
                            </ul>
                        </div>
                        {/* </div> */}
                    </div>

                    {/* <div className="col-lg-6"> */}
                    <div style={{
                        background: '#ffffff',
                        border: '2px solid #e5e7eb',
                        borderRadius: '12px',
                        padding: '2rem',
                        flex: 1
                    }}>
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1.5rem' }}>
                            <div style={{
                                background: '#f3f4f6',
                                borderRadius: '50%',
                                width: '60px',
                                height: '60px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginRight: '1rem'
                            }}>
                                <span style={{ fontSize: '24px' }}>🤖</span>
                            </div>
                            <Heading as="h3" style={{ margin: 0, color: '#374151' }}>
                                <Translate id="extension_welcome.differentiation.others_title">
                                    Traditional AI Assistants (Monica, etc.)
                                </Translate>
                            </Heading>
                        </div>

                        <div style={{ marginBottom: '1.5rem' }}>
                            <h4 style={{ color: '#6b7280', marginBottom: '1rem' }}>
                                <Translate id="extension_welcome.differentiation.others_focus">
                                    Focus: Task Completion
                                </Translate>
                            </h4>
                            <ul style={{ listStyle: 'none', padding: 0 }}>
                                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '0.8rem' }}>
                                    <span style={{ marginRight: '0.5rem', color: '#ef4444' }}>×</span>
                                    <Translate id="extension_welcome.differentiation.others_limitation1">
                                        Linear text responses without visual structure
                                    </Translate>
                                </li>
                                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '0.8rem' }}>
                                    <span style={{ marginRight: '0.5rem', color: '#ef4444' }}>×</span>
                                    <Translate id="extension_welcome.differentiation.others_limitation2">
                                        Single-perspective answers that may create echo chambers
                                    </Translate>
                                </li>
                                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '0.8rem' }}>
                                    <span style={{ marginRight: '0.5rem', color: '#ef4444' }}>×</span>
                                    <Translate id="extension_welcome.differentiation.others_limitation3">
                                        Limited critical thinking support and bias awareness
                                    </Translate>
                                </li>
                                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '0.8rem' }}>
                                    <span style={{ marginRight: '0.5rem', color: '#ef4444' }}>×</span>
                                    <Translate id="extension_welcome.differentiation.others_limitation4">
                                        Focus on quick answers rather than thinking development
                                    </Translate>
                                </li>
                            </ul>
                        </div>
                    </div>
                    {/* </div> */}
                </div>

                <div style={{
                    // background: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
                    backgroundColor: 'deepskyblue',
                    borderRadius: '12px',
                    padding: '2rem',
                    marginTop: '2rem',
                    textAlign: 'center',
                    color: 'white'
                }}>
                    <Heading as="h3" style={{ color: 'white', marginBottom: '1rem' }}>
                        <Translate id="extension_welcome.differentiation.philosophy_title">
                            Our Philosophy: AI Enhances Human Intelligence, Not Replaces It
                        </Translate>
                    </Heading>
                    <p style={{ fontSize: '1.1rem', margin: 0, opacity: 0.9 }}>
                        <Translate id="extension_welcome.differentiation.philosophy_description">
                            FunBlocks AI Assistant is designed to strengthen your cognitive abilities, helping you think more critically,
                            explore topics more comprehensively, and develop better analytical skills for the AI era.
                        </Translate>
                    </p>
                </div>
            </div>
        </section>
    );
}

function HeroSection({ setShowImageSrc, downloadExtension }) {
    return (
        <section className={clsx(styles.hero, styles.pageSection)} style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
        }}>
            {/* Background pattern */}
            <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'url("data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 100 100\'><defs><pattern id=\'grid\' width=\'10\' height=\'10\' patternUnits=\'userSpaceOnUse\'><path d=\'M 10 0 L 0 0 0 10\' fill=\'none\' stroke=\'rgba(255,255,255,0.1)\' stroke-width=\'0.5\'/></pattern></defs><rect width=\'100\' height=\'100\' fill=\'url(%23grid)\'/></svg>")',
                opacity: 0.3
            }} />

            <div className='container' style={{ position: 'relative', zIndex: 1 }}>
                <div className="row align-items-center" style={{ marginLeft: 10, marginRight: 10 }}>
                    <div className="col-lg-6">
                        <div className={styles.heroBadge} style={{
                            display: 'inline-block',
                            background: 'rgba(255, 255, 255, 0.2)',
                            color: 'white',
                            padding: '0.5rem 1rem',
                            borderRadius: '20px',
                            fontSize: '0.875rem',
                            fontWeight: '600',
                            marginBottom: '1.5rem',
                            backdropFilter: 'blur(10px)'
                        }}>
                            <Translate id="extension_welcome.hero.badge">🚀 AI-Powered Browser Extension</Translate>
                        </div>

                        <Heading as="h1" style={{
                            fontSize: 'clamp(2.5rem, 5vw, 3.5rem)',
                            fontWeight: '700',
                            marginBottom: '1.5rem',
                            lineHeight: '1.2'
                        }}>
                            <Translate id="extension_welcome.hero.title">
                                Transform Your Browsing with FunBlocks AI Assistant
                            </Translate>
                        </Heading>

                        <p style={{
                            fontSize: '1.25rem',
                            marginBottom: '2rem',
                            opacity: '0.9',
                            lineHeight: '1.6'
                        }}>
                            <Translate id="extension_welcome.hero.subtitle">
                                Your intelligent companion for AI-powered reading, writing, brainstorming, and critical thinking across the web
                            </Translate>
                        </p>

                        <div style={{
                            display: 'flex',
                            gap: '1rem',
                            marginBottom: '2rem',
                            flexWrap: 'wrap'
                        }}>
                            <Link
                                className="button button--primary button--lg"
                                onClick={downloadExtension}
                                style={{
                                    backgroundColor: 'white',
                                    color: '#667eea',
                                    border: 'none',
                                    fontWeight: '600',
                                    padding: '12px 24px',
                                    borderRadius: '8px',
                                    textDecoration: 'none',
                                    transition: 'all 0.3s ease'
                                }}
                            >
                                <Translate id="extension_welcome.hero.cta_primary">Install Free Extension</Translate>
                            </Link>
                            <Link
                                to="#features"
                                className="button button--secondary button--lg"
                                style={{
                                    backgroundColor: 'transparent',
                                    color: 'white',
                                    border: '2px solid rgba(255, 255, 255, 0.3)',
                                    fontWeight: '600',
                                    padding: '12px 24px',
                                    borderRadius: '8px',
                                    textDecoration: 'none',
                                    transition: 'all 0.3s ease'
                                }}
                            >
                                <Translate id="extension_welcome.hero.cta_secondary">Explore Features</Translate>
                            </Link>
                        </div>

                        {/* Key features highlights */}
                        <div style={{
                            display: 'flex',
                            gap: '2rem',
                            flexWrap: 'wrap',
                            fontSize: '0.9rem',
                            opacity: '0.8'
                        }}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                                <span>✨</span>
                                <Translate id="extension_welcome.hero.feature1">AI Brainstorming</Translate>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                                <span>💡</span>
                                <Translate id="extension_welcome.hero.feature2">Critical Thinking</Translate>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                                <span>📝</span>
                                <Translate id="extension_welcome.hero.feature3">Smart Writing</Translate>
                            </div>
                        </div>
                    </div>

                    <div className="col-lg-6">
                        <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            position: 'relative'
                        }}>
                            <div style={{
                                position: 'relative',
                                maxWidth: '600px',
                                width: '100%'
                            }}>
                                <img
                                    className={clsx(styles.heroImage, "shadow-lg")}
                                    src="/img/portfolio/thumbnails/aiflow_benefits.png"
                                    alt="FunBlocks AI Assistant interface showing brainstorming, mindmapping, and critical thinking tools"
                                    onClick={() => setShowImageSrc("/img/portfolio/fullsize/aiflow_benefits.png")}
                                    style={{
                                        width: '100%',
                                        height: 'auto',
                                        borderRadius: '12px',
                                        cursor: 'pointer',
                                        transition: 'transform 0.3s ease',
                                        boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)'
                                    }}
                                />
                                <div style={{
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    right: 0,
                                    bottom: 0,
                                    background: 'rgba(0, 0, 0, 0.3)',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    opacity: 0,
                                    transition: 'opacity 0.3s ease',
                                    borderRadius: '12px'
                                }}
                                    onMouseEnter={(e) => e.target.style.opacity = 1}
                                    onMouseLeave={(e) => e.target.style.opacity = 0}
                                >
                                    <span style={{
                                        color: 'white',
                                        fontWeight: '600',
                                        fontSize: '1.1rem'
                                    }}>
                                        <Translate id="extension_welcome.hero.image_overlay">Click to view full interface</Translate>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}

function SettingsSection() {
    return (
        <section id="features" className={styles.featureSection} style={{ backgroundColor: 'azure' }}>
            <div className="container">
                <Heading as="h2" className={styles.sectionTitle}>
                    <Translate id="extension_welcome.settings.title">AI-Powered Brainstorming & Mindmapping</Translate>
                </Heading>
                <p className={styles.sectionDescription}>
                    <Translate id="extension_welcome.settings.subtitle">Enhance your thinking with AI-assisted classic thinking models and frameworks</Translate>
                </p>

                <div className={styles.featureGrid}>
                    <div style={{ cursor: 'pointer', flex: 4 }}>
                        <img
                            className={styles.featureImage}
                            id="aiflow-brainstorming"
                            alt="AI-powered brainstorming and mindmapping tools with Six Thinking Hats, SWOT Analysis, and First Principles Thinking frameworks in FunBlocks AI Extension"
                            src="/img/portfolio/thumbnails/settings_llm_provider.png"
                        />
                    </div>

                    <div className={styles.featureContent} style={{
                        flex: 2,
                    }}>

                        <Heading as="h3" style={{ paddingTop: '10px' }}>
                            <Translate id="extension_welcome.settings.inbox_llms.title">Structured Thinking with Classic Models</Translate>
                        </Heading>
                        <ul className={styles.featureList}>
                            <li>
                                <Translate id="extension_welcome.settings.inbox_llms.li1">Six Thinking Hats Analysis</Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.settings.inbox_llms.li2">SWOT Analysis Framework</Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.settings.inbox_llms.li3">First Principles Thinking</Translate>
                            </li>
                        </ul>

                        <Heading as="h3" style={{ paddingTop: '10px' }}>
                            <Translate id="extension_welcome.settings.private_llms.title">Creative Output Generation</Translate>
                        </Heading>
                        <ul className={styles.featureList}>
                            <li>
                                <Translate id="extension_welcome.settings.private_llms.li1">Dynamic Presentation Slides</Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.settings.private_llms.li2">Comprehensive Solution Documents</Translate>
                            </li>
                        </ul>
                    </div>

                </div>
            </div>
        </section>
    );
}

function ReadingSection() {
    return (
        <section id="reading" className={styles.featureSection}>
            <div className="container">
                <Heading as="h2" className={styles.sectionTitle}>
                    <Translate id="extension_welcome.reading.title">AI-Enhanced Reading & Critical Thinking</Translate>
                </Heading>
                <p className={styles.sectionDescription}>
                    <Translate id="extension_welcome.reading.subtitle">Transform how you read and analyze content with AI-powered critical thinking tools</Translate>
                </p>

                <div className={styles.featureGrid}>
                    <div style={{ cursor: 'pointer', flex: 4 }}>
                        <img
                            className={styles.featureImage}
                            id="aiflow-brainstorming"
                            alt="FunBlocks AI sidebar assistant interface for enhanced reading with critical thinking analysis frameworks including Six Thinking Hats, SWOT Analysis, and McKinsey Problem-Solving Method"
                            src='/img/portfolio/thumbnails/ai_reading_en.png'
                        />
                    </div>
                    <div className={styles.featureContent} style={{
                        flex: 3,
                    }}>
                        <Heading as="h3">
                            <Translate id="extension_welcome.reading.critical_thinking">Critical Thinking Frameworks</Translate>
                        </Heading>
                        <ul className={styles.featureList}>
                            <li>
                                <Translate id="extension_welcome.reading.ct1">Six Thinking Hats Analysis</Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.reading.ct2">SWOT Analysis Framework</Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.reading.ct3">McKinsey Problem-Solving Method</Translate>
                            </li>
                        </ul>

                        <Heading as="h3" style={{ paddingTop: '10px' }}>
                            <Translate id="extension_welcome.reading.desc">Content Transformation Outputs</Translate>
                        </Heading>
                        <ul className={styles.featureList}>
                            <li>
                                <Translate id="extension_welcome.reading.li1">Professional Presentation Slides</Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.reading.li2">Comprehensive Solution Documents</Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.reading.li3">Visual Infographics & Insight Cards</Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.reading.li4">AI-Generated Explanatory Images</Translate>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    );
}

function ConvenienceSection() {
    return (
        <section id="convenience" className={styles.featureSection} style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
        }}>
            {/* Background pattern */}
            <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'url("data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 100 100\'><defs><pattern id=\'grid\' width=\'10\' height=\'10\' patternUnits=\'userSpaceOnUse\'><path d=\'M 10 0 L 0 0 0 10\' fill=\'none\' stroke=\'rgba(255,255,255,0.1)\' stroke-width=\'0.5\'/></pattern></defs><rect width=\'100\' height=\'100\' fill=\'url(%23grid)\'/></svg>")',
                opacity: 0.3
            }} />

            <div className="container" style={{ position: 'relative', zIndex: 1 }}>
                <Heading as="h2" className={styles.sectionTitle} style={{
                    color: 'white',
                    textAlign: 'center',
                    background: 'linear-gradient(45deg, #ffffff, #fbbf24, #ffffff)',
                    backgroundSize: '200% 200%',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    animation: 'gradient 3s ease infinite'
                }}>
                    <Translate id="extension_welcome.convenience.title">
                        告别繁琐流程，一键完成AI任务
                    </Translate>
                </Heading>
                <style jsx>{`
                    @keyframes gradient {
                        0% { background-position: 0% 50%; }
                        50% { background-position: 100% 50%; }
                        100% { background-position: 0% 50%; }
                    }
                `}</style>
                <p className={styles.sectionDescription} style={{
                    color: 'rgba(255, 255, 255, 0.9)',
                    textAlign: 'center',
                    fontSize: '1.2rem',
                    marginBottom: '3rem'
                }}>
                    <Translate id="extension_welcome.convenience.subtitle">
                        使用 FunBlocks AI Assistant 插件，再也不用拷贝、切换到ChatGPT页面、粘贴、写提示词这一繁琐的过程
                    </Translate>
                </p>

                {/* Before and After Comparison */}
                <div style={{
                    display: 'flex',
                    gap: '2rem',
                    alignItems: 'stretch',
                    marginBottom: '3rem',
                    flexWrap: 'wrap'
                }}>
                    {/* Traditional Way */}
                    {(() => {
                        const [isTraditionalHovered, setIsTraditionalHovered] = React.useState(false);

                        return (
                            <div style={{
                                flex: 1,
                                minWidth: '300px',
                                background: 'rgba(255, 255, 255, 0.1)',
                                borderRadius: '12px',
                                padding: '2rem',
                                backdropFilter: 'blur(10px)',
                                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                                cursor: 'pointer',
                                transform: isTraditionalHovered ? 'translateY(-5px)' : 'translateY(0)',
                                boxShadow: isTraditionalHovered ? '0 10px 30px rgba(255, 107, 107, 0.3)' : 'none'
                            }}
                                onMouseEnter={() => setIsTraditionalHovered(true)}
                                onMouseLeave={() => setIsTraditionalHovered(false)}>
                                <div style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    marginBottom: '1.5rem',
                                    color: '#ff6b6b'
                                }}>
                                    <span style={{ fontSize: '2rem', marginRight: '1rem' }}>😤</span>
                                    <Heading as="h3" style={{ color: 'white', margin: 0 }}>
                                        <Translate id="extension_welcome.convenience.traditional_title">
                                            传统方式：繁琐低效
                                        </Translate>
                                    </Heading>
                                </div>

                                <div style={{ marginBottom: '1rem' }}>
                                    {[1, 2, 3, 4, 5].map((step) => (
                                        <div key={step} style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            marginBottom: '0.8rem',
                                            opacity: 0.9
                                        }}>
                                            <div style={{
                                                background: '#ff6b6b',
                                                color: 'white',
                                                borderRadius: '50%',
                                                width: '24px',
                                                height: '24px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                fontSize: '0.8rem',
                                                fontWeight: 'bold',
                                                marginRight: '0.8rem',
                                                flexShrink: 0
                                            }}>
                                                {step}
                                            </div>
                                            <span>
                                                <Translate id={`extension_welcome.convenience.traditional_step${step}`}>
                                                </Translate>
                                            </span>
                                        </div>
                                    ))}
                                </div>

                                <div style={{
                                    background: 'rgba(255, 107, 107, 0.2)',
                                    borderRadius: '8px',
                                    padding: '1rem',
                                    marginTop: '1rem'
                                }}>
                                    <div style={{ fontSize: '0.9rem', opacity: 0.9 }}>
                                        ⏱️ <Translate id="extension_welcome.convenience.traditional_time">耗时：2-3分钟</Translate><br />
                                        🔄 <Translate id="extension_welcome.convenience.traditional_switches">页面切换：3-4次</Translate><br />
                                        📝 <Translate id="extension_welcome.convenience.traditional_steps">操作步骤：5步</Translate>
                                    </div>
                                </div>
                            </div>
                        );
                    })()}

                    {/* VS Arrow */}
                    <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        minWidth: '80px'
                    }}>
                        <div style={{
                            background: 'rgba(255, 255, 255, 0.2)',
                            borderRadius: '50%',
                            width: '60px',
                            height: '60px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '1.5rem',
                            fontWeight: 'bold',
                            marginBottom: '1rem'
                        }}>
                            VS
                        </div>
                        <div style={{ fontSize: '2rem' }}>⚡</div>
                    </div>

                    {/* FunBlocks Way */}
                    {(() => {
                        const [isFunBlocksHovered, setIsFunBlocksHovered] = React.useState(false);

                        return (
                            <div style={{
                                flex: 1,
                                minWidth: '300px',
                                background: 'rgba(255, 255, 255, 0.15)',
                                borderRadius: '12px',
                                padding: '2rem',
                                backdropFilter: 'blur(10px)',
                                border: '2px solid rgba(76, 222, 128, 0.3)',
                                transition: 'transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease',
                                cursor: 'pointer',
                                transform: isFunBlocksHovered ? 'translateY(-5px)' : 'translateY(0)',
                                boxShadow: isFunBlocksHovered ? '0 15px 40px rgba(76, 222, 128, 0.4)' : 'none',
                                borderColor: isFunBlocksHovered ? 'rgba(76, 222, 128, 0.6)' : 'rgba(76, 222, 128, 0.3)'
                            }}
                                onMouseEnter={() => setIsFunBlocksHovered(true)}
                                onMouseLeave={() => setIsFunBlocksHovered(false)}>
                                <div style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    marginBottom: '1.5rem',
                                    color: '#4ade80'
                                }}>
                                    <span style={{ fontSize: '2rem', marginRight: '1rem' }}>🚀</span>
                                    <Heading as="h3" style={{ color: 'white', margin: 0 }}>
                                        <Translate id="extension_welcome.convenience.funblocks_title">
                                            FunBlocks AI：一键完成
                                        </Translate>
                                    </Heading>
                                </div>

                                <div style={{ marginBottom: '1rem' }}>
                                    <div style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        marginBottom: '0.8rem'
                                    }}>
                                        <div style={{
                                            background: '#4ade80',
                                            color: 'white',
                                            borderRadius: '50%',
                                            width: '24px',
                                            height: '24px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            fontSize: '0.8rem',
                                            fontWeight: 'bold',
                                            marginRight: '0.8rem',
                                            flexShrink: 0
                                        }}>
                                            1
                                        </div>
                                        <span>
                                            <Translate id="extension_welcome.convenience.funblocks_step1">
                                                选择内容，点击AI工具栏
                                            </Translate>
                                        </span>
                                    </div>
                                    <div style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        marginBottom: '0.8rem'
                                    }}>
                                        <div style={{
                                            background: '#4ade80',
                                            color: 'white',
                                            borderRadius: '50%',
                                            width: '24px',
                                            height: '24px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            fontSize: '0.8rem',
                                            fontWeight: 'bold',
                                            marginRight: '0.8rem',
                                            flexShrink: 0
                                        }}>
                                            ✓
                                        </div>
                                        <span>
                                            <Translate id="extension_welcome.convenience.funblocks_step2">
                                                AI自动理解上下文，高质量完成任务
                                            </Translate>
                                        </span>
                                    </div>
                                </div>

                                <div style={{
                                    background: 'rgba(76, 222, 128, 0.2)',
                                    borderRadius: '8px',
                                    padding: '1rem',
                                    marginTop: '1rem'
                                }}>
                                    <div style={{ fontSize: '0.9rem' }}>
                                        ⚡ <Translate id="extension_welcome.convenience.funblocks_time">耗时：5-10秒</Translate><br />
                                        🎯 <Translate id="extension_welcome.convenience.funblocks_switches">页面切换：0次</Translate><br />
                                        ✨ <Translate id="extension_welcome.convenience.funblocks_steps">操作步骤：1步</Translate>
                                    </div>
                                </div>
                            </div>
                        );
                    })()}
                </div>

                {/* Key Benefits */}
                <div style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    borderRadius: '12px',
                    padding: '2rem',
                    textAlign: 'center',
                    backdropFilter: 'blur(10px)'
                }}>
                    <Heading as="h3" style={{ color: 'white', marginBottom: '1.5rem' }}>
                        <Translate id="extension_welcome.convenience.benefits_title">
                            核心优势
                        </Translate>
                    </Heading>
                    <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                        gap: '1.5rem'
                    }}>
                        {[
                            { icon: '⚡', titleId: 'extension_welcome.convenience.benefit1_title', descId: 'extension_welcome.convenience.benefit1_desc' },
                            { icon: '🎯', titleId: 'extension_welcome.convenience.benefit2_title', descId: 'extension_welcome.convenience.benefit2_desc' },
                            { icon: '🔄', titleId: 'extension_welcome.convenience.benefit3_title', descId: 'extension_welcome.convenience.benefit3_desc' },
                            { icon: '✨', titleId: 'extension_welcome.convenience.benefit4_title', descId: 'extension_welcome.convenience.benefit4_desc' }
                        ].map((benefit, index) => {
                            const BenefitCard = () => {
                                const [isHovered, setIsHovered] = React.useState(false);

                                return (
                                    <div
                                        key={index}
                                        style={{
                                            padding: '1rem',
                                            borderRadius: '8px',
                                            transition: 'transform 0.3s ease, background-color 0.3s ease',
                                            cursor: 'pointer',
                                            transform: isHovered ? 'translateY(-3px)' : 'translateY(0)',
                                            backgroundColor: isHovered ? 'rgba(255, 255, 255, 0.1)' : 'transparent'
                                        }}
                                        onMouseEnter={() => setIsHovered(true)}
                                        onMouseLeave={() => setIsHovered(false)}
                                    >
                                        <div style={{ fontSize: '2rem', marginBottom: '0.5rem', pointerEvents: 'none' }}>
                                            {benefit.icon}
                                        </div>
                                        <div style={{ fontWeight: 'bold', marginBottom: '0.5rem', pointerEvents: 'none' }}>
                                            <Translate id={benefit.titleId}></Translate>
                                        </div>
                                        <div style={{ fontSize: '0.9rem', opacity: 0.9, pointerEvents: 'none' }}>
                                            <Translate id={benefit.descId}></Translate>
                                        </div>
                                    </div>
                                );
                            };

                            return <BenefitCard key={index} />;
                        })}
                    </div>
                </div>
            </div>
        </section>
    );
}

function ContextualSection() {
    return (
        <section id="contextual" className={styles.featureSection} style={{ backgroundColor: 'azure' }}>
            <div className="container">
                <Heading as="h2" className={styles.sectionTitle}>
                    <Translate id="extension_welcome.contextual.title">Contextual AI Tools & Smart Widgets</Translate>
                </Heading>
                <p className={styles.sectionDescription}>
                    <Translate id="extension_welcome.contextual.subtitle">Access powerful AI features instantly with context-aware toolbars and smart widgets</Translate>
                </p>

                <div className={styles.featureGrid}>
                    <div style={{ cursor: 'pointer', flex: 4 }}>
                        <img
                            className={styles.featureImage}
                            src="/img/portfolio/fullsize/contextual_toolbar.png"
                            alt="FunBlocks AI contextual toolbar for quick text-based actions including brainstorming, mindmapping, critical thinking analysis, translation, explanation, and text polishing"
                        />
                    </div>

                    <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'flex-end',
                        flex: 3,
                        textAlign: 'end'
                    }}>
                        <Heading as="h3" style={{ paddingTop: '10px' }}>
                            <Translate id="extension_welcome.contextual.toolbar">Smart Contextual Toolbar</Translate>
                        </Heading>
                        <span className="text-right">
                            <Translate id="extension_welcome.contextual.toolbar_desc">
                                Translate, explain, polish, and continue writing with one click - AI assistant always at the ready for any selected text
                            </Translate>
                        </span>
                        <Heading as="h4" style={{ paddingTop: '10px', color: 'dodgerblue' }}>
                            <Translate id="extension_welcome.contextual.try_now">Try it now, select the text below:</Translate>
                        </Heading>
                        <span className="mb-3" style={{ backgroundColor: 'white', padding: '5px 10px', borderRadius: '8px' }}>
                            <Translate id="extension_welcome.contextual.select_text">
                                FunBlocks AI: Your all-in-one smart reading and writing assistant with brainstorming and critical thinking tools
                            </Translate>
                        </span>
                    </div>
                </div>

                <div className={styles.featureGrid} style={{
                    flexDirection: 'row',
                    width: '100%',
                    gap: '2rem',
                    marginTop: '6rem'
                }}>
                    <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'end',
                        gap: 10,
                        flex: 4
                    }}>
                        <img
                            className={clsx(styles.featureImage, "mb-3", "shadow-lg")}
                            style={{ width: '400px' }}
                            src="/img/portfolio/fullsize/contextual_widget_email.png"
                            alt="FunBlocks AI email widget for generating professional replies in Gmail with AI-powered brainstorming and critical thinking assistance"
                        />
                        <img
                            className={clsx(styles.featureImage, "mb-2", "shadow-lg")}
                            style={{ width: '400px' }}
                            src="/img/portfolio/fullsize/contextual_widget_video.png"
                            alt="FunBlocks AI video widget for summarizing and analyzing YouTube content with critical thinking frameworks including Six Thinking Hats and SWOT Analysis"
                        />
                    </div>

                    <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        flex: 3,
                    }}>
                        <Heading as="h3" style={{ paddingTop: '10px' }}>
                            <Translate id="extension_welcome.contextual.widget">Intelligent Context-Aware Widgets</Translate>
                        </Heading>
                        <span className="text-right">
                            <Translate id="extension_welcome.contextual.widget_desc">
                                Smart widgets automatically appear when needed - email assistant for Gmail, video summarizer for YouTube, and more specialized tools across the web
                            </Translate>
                        </span>
                    </div>
                </div>
            </div>
        </section>
    );
}

function MindmapDemoSection({ setShowImageSrc }) {
    return (
        <section id="mindmap-demo" className={styles.featureSection} style={{ backgroundColor: 'white' }}>
            <div className="container">
                <Heading as="h2" className={styles.sectionTitle}>
                    <Translate id="extension_welcome.mindmap_demo.title">
                        One-Click Web Page to Mindmap Transformation
                    </Translate>
                </Heading>
                <p className={styles.sectionDescription}>
                    <Translate id="extension_welcome.mindmap_demo.subtitle">
                        Transform any web page into a comprehensive mindmap instantly with AI-powered analysis
                    </Translate>
                </p>

                <div className={styles.featureGrid}>
                    <div style={{ cursor: 'pointer', flex: 4 }}>
                        <img
                            className={styles.featureImage}
                            src="/img/portfolio/fullsize/ai-mindmap-extension-demo.png"
                            alt="FunBlocks AI Extension one-click mindmap generation from web page content with comprehensive visual analysis and structured thinking"
                            onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai-mindmap-extension-demo.png")}
                            style={{
                                width: '100%',
                                height: 'auto',
                                borderRadius: '12px',
                                cursor: 'pointer',
                                transition: 'transform 0.3s ease',
                                boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)'
                            }}
                        />
                    </div>

                    <div className={styles.featureContent} style={{ flex: 3 }}>
                        <Heading as="h3" style={{ paddingTop: '10px' }}>
                            <Translate id="extension_welcome.mindmap_demo.benefits_title">
                                Key Benefits of Mindmap Visualization
                            </Translate>
                        </Heading>
                        <ul className={styles.featureList}>
                            <li>
                                <Translate id="extension_welcome.mindmap_demo.benefit1">
                                    Instant visual comprehension of complex information
                                </Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.mindmap_demo.benefit2">
                                    Hierarchical structure reveals key relationships and connections
                                </Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.mindmap_demo.benefit3">
                                    Enhanced memory retention through visual learning
                                </Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.mindmap_demo.benefit4">
                                    Quick identification of main topics and supporting details
                                </Translate>
                            </li>
                        </ul>

                        <Heading as="h3" style={{ paddingTop: '20px' }}>
                            <Translate id="extension_welcome.mindmap_demo.features_title">
                                Advanced Mindmap Features
                            </Translate>
                        </Heading>
                        <ul className={styles.featureList}>
                            <li>
                                <Translate id="extension_welcome.mindmap_demo.feature1">
                                    AI-powered content analysis and categorization
                                </Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.mindmap_demo.feature2">
                                    Interactive nodes with expandable details
                                </Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.mindmap_demo.feature3">
                                    Export to multiple formats for sharing and collaboration
                                </Translate>
                            </li>
                            <li>
                                <Translate id="extension_welcome.mindmap_demo.feature4">
                                    Integration with brainstorming and critical thinking tools
                                </Translate>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    );
}

function WritingSection() {
    return (
        <section id="writing" className={styles.featureSection} style={{ backgroundColor: 'floralwhite' }}>
            <div className="container">
                <Heading as="h2" className={styles.sectionTitle}>
                    <Translate id="extension_welcome.writing.title">AI-Powered Writing & Content Creation</Translate>
                </Heading>
                <p className={styles.sectionDescription}>
                    <Translate id="extension_welcome.writing.subtitle">Enhance your writing with intelligent AI assistance for any text field across the web</Translate>
                </p>

                <div className={styles.featureGrid}>
                    <div className="d-flex flex-column align-items-end mb-3" style={{ flex: 2 }}>
                        <textarea
                            id="editor_demo"
                            rows={15}
                            style={{ width: '100%', outline: 'none', padding: '8px' }}
                            defaultValue="Try FunBlocks AI Writing Assistant - your creative partner for brainstorming, drafting, and polishing content with critical thinking frameworks"
                        />
                    </div>
                    <div className="d-flex flex-column align-items-start justify-content-between">
                        <div>
                            <h3>
                                <Translate id="extension_welcome.writing.methods">Multiple Ways to Access AI Writing Tools:</Translate>
                            </h3>
                            <ul style={{ fontSize: '18px' }}>
                                <li>
                                    <Translate id="extension_welcome.writing.li1">Select text to activate the smart contextual toolbar</Translate>
                                </li>
                                <li>
                                    <Translate id="extension_welcome.writing.li2">Click the FunBlocks AI icon in any text field</Translate>
                                </li>
                                <li>
                                    <Translate id="extension_welcome.writing.li3">Type '/' for quick command access to all writing tools</Translate>
                                </li>
                            </ul>
                        </div>
                        <div>
                            <h3>
                                <Translate id="writing.features">Advanced Writing Capabilities:</Translate>
                            </h3>
                            <ul style={{ fontSize: '18px' }}>
                                <li>
                                    <Translate id="extension_welcome.writing.f1">AI-powered article generation with structured thinking frameworks</Translate>
                                </li>
                                <li>
                                    <Translate id="extension_welcome.writing.f2">Professional text polishing with tone and style adjustment</Translate>
                                </li>
                                <li>
                                    <Translate id="extension_welcome.writing.f3">Smart content expansion with brainstorming capabilities</Translate>
                                </li>
                                <li>
                                    <Translate id="extension_welcome.writing.f4">Context-aware responses for emails, social media, and forums</Translate>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}


export default function WelcomeExtension() {
    const [showImageSrc, setShowImageSrc] = useState(null);

    const [browser, setBrowser] = useState('Browser');

    useEffect(() => {
        const detectBrowser = () => {
            const userAgent = navigator.userAgent;
            if (userAgent.includes("Edg")) {
                return 'Edge';
            } else if (userAgent.includes("Chrome")) {
                return 'Chrome';
            }
            return 'Browser';
        };

        setBrowser(detectBrowser());
    }, []);

    const downloadExtension = () => {
        if (browser === 'Edge') {
            window.open("https://microsoftedge.microsoft.com/addons/detail/funblocks-ai-your-ultim/lmmlojdklhcdiefaniakpkhhdmamnigk", '_blank');
        } else if (browser === "Chrome") {
            window.open("https://chrome.google.com/webstore/detail/coodnehmocjfaandkbeknihiagfccoid", '_blank');
        } else {
            alert("We only support Chrome and Edge browsers. It is recommended to use Chrome for the best experience.");
        }
    };

    return (
        <Layout
            title={translate({
                id: 'head.title',
                message: "FunBlocks AI Browser Extension - AI-Powered Brainstorming, Mindmapping & Critical Thinking Tools | Boost Productivity"
            })}
            description={translate({
                id: 'head.description',
                message: "Transform your browsing with FunBlocks AI Extension. Features AI-powered brainstorming, mindmapping, critical thinking frameworks (Six Thinking Hats, SWOT Analysis), and creative writing tools. Generate infographics, insight cards, and visual mind maps. Compatible with ChatGPT, Claude, Gemini Pro. Enhance how you read, write, and think online."
            })}
        >
            {/* Add structured data for SEO */}
            <ExtensionStructuredData />

            <HeroSection setShowImageSrc={setShowImageSrc} downloadExtension={downloadExtension} />
            <BenefitsSection page={'extension_welcome'} />

            {/* Add convenience section to highlight the key advantage */}
            <ConvenienceSection />

            <SettingsSection setShowImageSrc={setShowImageSrc} />
            <ReadingSection />
            <ContextualSection />
            <WritingSection />

            {/* Add differentiation section */}
            <DifferentiationSection />

            {/* Add mindmap demo section */}
            <MindmapDemoSection setShowImageSrc={setShowImageSrc} />

            {/* Add new comparison section */}
            <ComparisonSection
                page={'extension_welcome'}
                competitors={{
                    funblocks: {
                        label: <Translate id="extension_welcome.comparison.funblocksHeader">FunBlocks AI</Translate>,
                        isHighlighted: true
                    },
                    monica: {
                        label: <Translate id="extension_welcome.comparison.chatgptHeader">Monica</Translate>,
                        isHighlighted: false
                    },
                    grammarly: {
                        label: <Translate id="extension_welcome.comparison.grammarlyHeader">Grammarly</Translate>,
                        isHighlighted: false
                    },
                    other: {
                        label: <Translate id="extension_welcome.comparison.otherHeader">Other AI Extensions</Translate>,
                        isHighlighted: false
                    }
                }}
                customData={[
                    {
                        feature: <Translate id="extension_welcome.comparison.feature1">AI-Powered Brainstorming</Translate>,
                        funblocks: true,
                        monica: false,
                        grammarly: false,
                        other: 'Limited'
                    },
                    {
                        feature: <Translate id="extension_welcome.comparison.feature2">Visual Mind Mapping</Translate>,
                        funblocks: true,
                        monica: 'Limited',
                        grammarly: false,
                        other: false
                    },
                    {
                        feature: <Translate id="extension_welcome.comparison.feature3">Critical Thinking Frameworks</Translate>,
                        funblocks: true,
                        monica: false,
                        grammarly: false,
                        other: 'Limited'
                    },
                    {
                        feature: <Translate id="extension_welcome.comparison.feature4">Contextual AI Toolbar</Translate>,
                        funblocks: true,
                        monica: true,
                        grammarly: 'Limited',
                        other: 'Limited'
                    },
                    {
                        feature: <Translate id="extension_welcome.comparison.feature5">Infographic & Insight Card Creation</Translate>,
                        funblocks: true,
                        monica: false,
                        grammarly: false,
                        other: false
                    },
                    {
                        feature: <Translate id="extension_welcome.comparison.feature6">Multi-Model AI Support</Translate>,
                        funblocks: true,
                        monica: true,
                        grammarly: false,
                        other: 'Limited'
                    },
                    {
                        feature: <Translate id="extension_welcome.comparison.feature7">Context-Aware Widgets</Translate>,
                        funblocks: true,
                        monica: true,
                        grammarly: 'Limited',
                        other: 'Limited'
                    },
                    {
                        feature: <Translate id="extension_welcome.comparison.feature8">Free Version Available</Translate>,
                        funblocks: true,
                        monica: true,
                        grammarly: true,
                        other: 'Varies'
                    }
                ]}
                noteTranslateId="extension_welcome.comparison.note"
            />

            {/* Add video demonstration section */}
            <VideoSection
                page={'extension_welcome'}
                videoId="dtLVO7hPaBY"
                customFeatures={[
                    {
                        icon: '✍️',
                        title: <Translate id="extension_welcome.video.feature1.title">AI Writing Assistant</Translate>,
                        description: <Translate id="extension_welcome.video.feature1.description">
                            Enhance your writing with AI-powered tools for content creation, editing, and brainstorming
                        </Translate>
                    },
                    {
                        icon: '📚',
                        title: <Translate id="extension_welcome.video.feature2.title">AI Reading Assistant</Translate>,
                        description: <Translate id="extension_welcome.video.feature2.description">
                            Transform how you read online with AI-powered summarization, analysis, and visualization
                        </Translate>
                    },
                    {
                        icon: '⚡',
                        title: <Translate id="extension_welcome.video.feature3.title">Cognitive Boosting Tools</Translate>,
                        description: <Translate id="extension_welcome.video.feature3.description">
                            Elevate your thinking with structured frameworks, mindmapping, and critical analysis tools
                        </Translate>
                    }
                ]}
            />

            <CTASection page={'extension_welcome'} toApp={downloadExtension} />

            {/* Add new use cases section */}
            <UseCasesSection page={'extension_welcome'} />
            <TestimonialsSection avatars={["👩‍🏫", "👨‍💼", "👨‍🎓", "🧑‍💻", "👩‍🎓", "👨‍💼"]} page={"extension_welcome"} />

            <FAQSection
                page={'extension_welcome'}
                faqIds={[
                    'q1', 'q2', 'q3', 'q4', 'q5', 'q8', 'q10', 'q11', 'q12',
                    'q13', 'q14', 'q15', 'q16', 'q17', 'q18'
                ]}
            />

            <Footer />
            {showImageSrc && <ImageModal imageSrc={showImageSrc} setImageSrc={setShowImageSrc} />}
            <GoogleAccountAnalytics page={'extension_welcome'} />
        </Layout>
    );
}